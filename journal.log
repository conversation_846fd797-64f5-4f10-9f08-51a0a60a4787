This is pdfTeX, Version 3.141592653-2.6-1.40.22 (TeX Live 2022/dev/Debian) (preloaded format=pdflatex 2025.7.21)  25 JUL 2025 15:20
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**journal.tex
(./journal.tex
LaTeX2e <2021-11-15> patch level 1
L3 programming layer <2022-01-21>
(/usr/share/texlive/texmf-dist/tex/latex/base/article.cls
Document Class: article 2021/10/04 v1.4n Standard LaTeX document class
(/usr/share/texlive/texmf-dist/tex/latex/base/size11.clo
File: size11.clo 2021/10/04 v1.4n Standard LaTeX file (size option)
)
\c@part=\count185
\c@section=\count186
\c@subsection=\count187
\c@subsubsection=\count188
\c@paragraph=\count189
\c@subparagraph=\count190
\c@figure=\count191
\c@table=\count192
\abovecaptionskip=\skip47
\belowcaptionskip=\skip48
\bibindent=\dimen138
)
(/usr/share/texlive/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2021/02/14 v1.3d Input encoding file
\inpenc@prehook=\toks16
\inpenc@posthook=\toks17
)
(/usr/share/texlive/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)
(/usr/share/texlive/texmf-dist/tex/generic/babel/babel.sty
Package: babel 2022/01/26 3.70 The Babel package
\babel@savecnt=\count193
\U@D=\dimen139
\l@unhyphenated=\language7

(/usr/share/texlive/texmf-dist/tex/generic/babel/txtbabel.def)
\bbl@readstream=\read2
\bbl@dirlevel=\count194


! Package babel Error: Unknown option 'italian'. Either you misspelled it
(babel)                or the language definition file italian.ldf was not foun
d.

See the babel package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.4254 \ProcessOptions*
                       
? 
Package babel Info: You haven't specified a language. I'll use 'nil'
(babel)             as the main language. Reported on input line 4299.
(/usr/share/texlive/texmf-dist/tex/generic/babel/nil.ldf
Language: nil 2022/01/26 3.70 Nil language
\l@nil=\language8
))
(/usr/share/texlive/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(/usr/share/texlive/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2014/10/28 v1.15 key=value parser (DPC)
\KV@toks@=\toks18
)
(/usr/share/texlive/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(/usr/share/texlive/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2020/03/06 v1.0d TeX engine tests
))
\Gm@cnth=\count195
\Gm@cntv=\count196
\c@Gm@tempcnt=\count197
\Gm@bindingoffset=\dimen140
\Gm@wd@mp=\dimen141
\Gm@odd@mp=\dimen142
\Gm@even@mp=\dimen143
\Gm@layoutwidth=\dimen144
\Gm@layoutheight=\dimen145
\Gm@layouthoffset=\dimen146
\Gm@layoutvoffset=\dimen147
\Gm@dimlist=\toks19
)
(/usr/share/texlive/texmf-dist/tex/latex/psnfss/mathptmx.sty
Package: mathptmx 2020/03/25 PSNFSS-v9.3 Times w/ Math, improved (SPQR, WaS) 
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 28.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/ztmcm/m/n on input line 28.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/ztmcm/m/n on input line 28.
LaTeX Font Info:    Redeclaring symbol font `letters' on input line 29.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/cmm/m/it --> OML/ztmcm/m/it on input line 29.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/cmm/b/it --> OML/ztmcm/m/it on input line 29.
LaTeX Font Info:    Redeclaring symbol font `symbols' on input line 30.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> OMS/ztmcm/m/n on input line 30.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> OMS/ztmcm/m/n on input line 30.
LaTeX Font Info:    Redeclaring symbol font `largesymbols' on input line 31.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> OMX/ztmcm/m/n on input line 31.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> OMX/ztmcm/m/n on input line 31.
\symbold=\mathgroup4
\symitalic=\mathgroup5
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 34.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> OT1/ptm/bx/n on input line 34.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/ptm/bx/n on input line 34.
LaTeX Font Info:    Redeclaring math alphabet \mathit on input line 35.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/ptm/m/it on input line 35.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/ptm/m/it on input line 35.
LaTeX Info: Redefining \hbar on input line 50.
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(/usr/share/texlive/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2021/03/04 v1.4d Standard LaTeX Graphics (DPC,SPQR)

(/usr/share/texlive/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.

(/usr/share/texlive/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2020/10/05 v1.2a Graphics/color driver for pdftex
))
\Gin@req@height=\dimen148
\Gin@req@width=\dimen149
)
(/usr/share/texlive/texmf-dist/tex/latex/titlesec/titlesec.sty
Package: titlesec 2021/07/05 v2.14 Sectioning titles
\ttl@box=\box50
\beforetitleunit=\skip49
\aftertitleunit=\skip50
\ttl@plus=\dimen150
\ttl@minus=\dimen151
\ttl@toksa=\toks20
\titlewidth=\dimen152
\titlewidthlast=\dimen153
\titlewidthfirst=\dimen154
)
(/usr/share/texlive/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2019/06/20 v3.9 Customized lists
\labelindent=\skip51
\enit@outerparindent=\dimen155
\enit@toks=\toks21
\enit@inbox=\box51
\enit@count@id=\count198
\enitdp@description=\count199
)
(/usr/share/texlive/texmf-dist/tex/latex/tools/array.sty
Package: array 2021/10/04 v2.5f Tabular extension package (FMi)
\col@sep=\dimen156
\ar@mcellbox=\box52
\extrarowheight=\dimen157
\NC@list=\toks22
\extratabsurround=\skip52
\backup@length=\skip53
\ar@cellbox=\box53
)
(/usr/share/texlive/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2021-06-07 v7.00m Hypertext links for LaTeX

(/usr/share/texlive/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2020-05-10 v1.25 LaTeX kernel commands for general use (HO)
)
(/usr/share/texlive/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(/usr/share/texlive/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)
(/usr/share/texlive/texmf-dist/tex/generic/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2019/12/15 v1.18 Key value parser (HO)
)
(/usr/share/texlive/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(/usr/share/texlive/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
)
(/usr/share/texlive/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(/usr/share/texlive/texmf-dist/tex/latex/letltxmacro/letltxmacro.sty
Package: letltxmacro 2019/12/03 v1.6 Let assignment for LaTeX macros (HO)
)
(/usr/share/texlive/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
)
(/usr/share/texlive/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2020-10-07 v3.14 Key value format for package options (HO)
)
\@linkdim=\dimen158
\Hy@linkcounter=\count266
\Hy@pagecounter=\count267

(/usr/share/texlive/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2021-06-07 v7.00m Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
)
(/usr/share/texlive/texmf-dist/tex/latex/hyperref/hyperref-langpatches.def
File: hyperref-langpatches.def 2021-06-07 v7.00m Hyperref: patches for babel la
nguages
)
(/usr/share/texlive/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
(/usr/share/texlive/texmf-dist/tex/generic/etexcmds/etexcmds.sty
Package: etexcmds 2019/12/15 v1.7 Avoid name clashes with e-TeX commands (HO)
)
\Hy@SavedSpaceFactor=\count268

(/usr/share/texlive/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2021-06-07 v7.00m Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4192.
Package hyperref Info: Link nesting OFF on input line 4197.
Package hyperref Info: Hyper index ON on input line 4200.
Package hyperref Info: Plain pages OFF on input line 4207.
Package hyperref Info: Backreferencing OFF on input line 4212.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4445.
\c@Hy@tempcnt=\count269

(/usr/share/texlive/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip16
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4804.
\XeTeXLinkMargin=\dimen159

(/usr/share/texlive/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(/usr/share/texlive/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count270
\Field@Width=\dimen160
\Fld@charsize=\dimen161
Package hyperref Info: Hyper figures OFF on input line 6076.
Package hyperref Info: Link nesting OFF on input line 6081.
Package hyperref Info: Hyper index ON on input line 6084.
Package hyperref Info: backreferencing OFF on input line 6091.
Package hyperref Info: Link coloring OFF on input line 6096.
Package hyperref Info: Link coloring with OCG OFF on input line 6101.
Package hyperref Info: PDF/A mode OFF on input line 6106.
LaTeX Info: Redefining \ref on input line 6146.
LaTeX Info: Redefining \pageref on input line 6150.

(/usr/share/texlive/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count271
\c@Item=\count272
\c@Hfootnote=\count273
)
Package hyperref Info: Driver (autodetected): hpdftex.

(/usr/share/texlive/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2021-06-07 v7.00m Hyperref driver for pdfTeX

(/usr/share/texlive/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count274
\c@bookmark@seq@number=\count275

(/usr/share/texlive/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2019/12/05 v1.9 Rerun checks for auxiliary files (HO)

(/usr/share/texlive/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
86.
)
\Hy@SectionHShift=\skip54
)
(/usr/share/texlive/texmf-dist/tex/latex/tools/multicol.sty
Package: multicol 2021/10/28 v1.9b multicolumn formatting (FMi)
\c@tracingmulticols=\count276
\mult@box=\box54
\multicol@leftmargin=\dimen162
\c@unbalance=\count277
\c@collectmore=\count278
\doublecol@number=\count279
\multicoltolerance=\count280
\multicolpretolerance=\count281
\full@width=\dimen163
\page@free=\dimen164
\premulticols=\dimen165
\postmulticols=\dimen166
\multicolsep=\skip55
\multicolbaselineskip=\skip56
\partial@page=\box55
\last@line=\box56
\maxbalancingoverflow=\dimen167
\mult@rightbox=\box57
\mult@grightbox=\box58
\mult@firstbox=\box59
\mult@gfirstbox=\box60
\@tempa=\box61
\@tempa=\box62
\@tempa=\box63
\@tempa=\box64
\@tempa=\box65
\@tempa=\box66
\@tempa=\box67
\@tempa=\box68
\@tempa=\box69
\@tempa=\box70
\@tempa=\box71
\@tempa=\box72
\@tempa=\box73
\@tempa=\box74
\@tempa=\box75
\@tempa=\box76
\@tempa=\box77
\@tempa=\box78
\@tempa=\box79
\@tempa=\box80
\@tempa=\box81
\@tempa=\box82
\@tempa=\box83
\@tempa=\box84
\@tempa=\box85
\@tempa=\box86
\@tempa=\box87
\@tempa=\box88
\@tempa=\box89
\@tempa=\box90
\@tempa=\box91
\@tempa=\box92
\@tempa=\box93
\@tempa=\box94
\@tempa=\box95
\@tempa=\box96
\c@minrows=\count282
\c@columnbadness=\count283
\c@finalcolumnbadness=\count284
\last@try=\dimen168
\multicolovershoot=\dimen169
\multicolundershoot=\dimen170
\mult@nat@firstbox=\box97
\colbreak@box=\box98
\mc@col@check@num=\count285
)
(/usr/share/texlive/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2020/10/26 v3.5g Customizing captions (AR)

(/usr/share/texlive/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2020/10/21 v2.2e caption3 kernel (AR)
\captionmargin=\dimen171
\captionmargin@=\dimen172
\captionwidth=\dimen173
\caption@tempdima=\dimen174
\caption@indent=\dimen175
\caption@parindent=\dimen176
\caption@hangindent=\dimen177
Package caption Info: Standard document class detected.
)
\c@caption@flags=\count286
\c@continuedfloat=\count287
Package caption Info: hyperref package is loaded.
)
(/usr/share/texlive/texmf-dist/tex/latex/pgfplots/pgfplots.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplots.revision.tex)
Package: pgfplots 2021/05/15 v1.18.1 Data Visualization (1.18.1)

(/usr/share/texlive/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks23
\pgfutil@tempdima=\dimen178
\pgfutil@tempdimb=\dimen179

(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-common-lists.t
ex)) (/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box99
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2021/05/15 v3.1.9a (3.1.9a)
))
Package: pgf 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks24
\pgfkeys@temptoks=\toks25

(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeysfiltered.code.t
ex
\pgfkeys@tmptoks=\toks26
))
\pgf@x=\dimen180
\pgf@y=\dimen181
\pgf@xa=\dimen182
\pgf@ya=\dimen183
\pgf@xb=\dimen184
\pgf@yb=\dimen185
\pgf@xc=\dimen186
\pgf@yc=\dimen187
\pgf@xd=\dimen188
\pgf@yd=\dimen189
\w@pgf@writea=\write3
\r@pgf@reada=\read3
\c@pgf@counta=\count288
\c@pgf@countb=\count289
\c@pgf@countc=\count290
\c@pgf@countd=\count291
\t@pgf@toka=\toks27
\t@pgf@tokb=\toks28
\t@pgf@tokc=\toks29
\pgf@sys@id@count=\count292
 (/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2021/05/15 v3.1.9a (3.1.9a)
)
Driver file for pgf: pgfsys-pdftex.def

(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-pdftex.def
File: pgfsys-pdftex.def 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.de
f
File: pgfsys-common-pdf.def 2021/05/15 v3.1.9a (3.1.9a)
)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.
tex
File: pgfsyssoftpath.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfsyssoftpath@smallbuffer@items=\count293
\pgfsyssoftpath@bigbuffer@items=\count294
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.
tex
File: pgfsysprotocol.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)) (/usr/share/texlive/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2021/10/31 v2.13 LaTeX color extensions (UK)

(/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 227.
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1352.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1356.
Package xcolor Info: Model `RGB' extended on input line 1368.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1370.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1372.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1373.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1374.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1375.
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen190
\pgfmath@count=\count295
\pgfmath@box=\box100
\pgfmath@toks=\toks30
\pgfmath@stack@operand=\toks31
\pgfmath@stack@operation=\toks32
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code
.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonomet
ric.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.cod
e.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison
.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.
tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code
.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.
tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerari
thmetics.code.tex)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count296
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfint.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.te
x
File: pgfcorepoints.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@picminx=\dimen191
\pgf@picmaxx=\dimen192
\pgf@picminy=\dimen193
\pgf@picmaxy=\dimen194
\pgf@pathminx=\dimen195
\pgf@pathmaxx=\dimen196
\pgf@pathminy=\dimen197
\pgf@pathmaxy=\dimen198
\pgf@xx=\dimen199
\pgf@xy=\dimen256
\pgf@yx=\dimen257
\pgf@yy=\dimen258
\pgf@zx=\dimen259
\pgf@zy=\dimen260
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.
code.tex
File: pgfcorepathconstruct.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@path@lastx=\dimen261
\pgf@path@lasty=\dimen262
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code
.tex
File: pgfcorepathusage.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@shorten@end@additional=\dimen263
\pgf@shorten@start@additional=\dimen264
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.te
x
File: pgfcorescopes.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfpic=\box101
\pgf@hbox=\box102
\pgf@layerbox@main=\box103
\pgf@picture@serial@count=\count297
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.c
ode.tex
File: pgfcoregraphicstate.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgflinewidth=\dimen265
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformation
s.code.tex
File: pgfcoretransformations.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@pt@x=\dimen266
\pgf@pt@y=\dimen267
\pgf@pt@temp=\dimen268
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.t
ex
File: pgfcoreobjects.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing
.code.tex
File: pgfcorepathprocessing.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.te
x
File: pgfcorearrows.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfarrowsep=\dimen269
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@max=\dimen270
\pgf@sys@shading@range@num=\count298
\pgf@shadingcount=\count299
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.
tex
File: pgfcoreexternal.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfexternal@startupbox=\box104
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.te
x
File: pgfcorelayers.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.c
ode.tex
File: pgfcoretransparency.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.
tex
File: pgfcorepatterns.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfnodeparttextbox=\box105
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65
.sty
Package: pgfcomp-version-0-65 2021/05/15 v3.1.9a (3.1.9a)
\pgf@nodesepstart=\dimen271
\pgf@nodesepend=\dimen272
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18
.sty
Package: pgfcomp-version-1-18 2021/05/15 v3.1.9a (3.1.9a)
)) (/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgffor.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex))
(/usr/share/texlive/texmf-dist/tex/latex/pgf/math/pgfmath.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)
\pgffor@iter=\dimen273
\pgffor@skip=\dimen274
\pgffor@stack=\toks33
\pgffor@toks=\toks34
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers
.code.tex
File: pgflibraryplothandlers.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@plot@mark@count=\count300
\pgfplotmarksize=\dimen275
)
\tikz@lastx=\dimen276
\tikz@lasty=\dimen277
\tikz@lastxsaved=\dimen278
\tikz@lastysaved=\dimen279
\tikz@lastmovetox=\dimen280
\tikz@lastmovetoy=\dimen281
\tikzleveldistance=\dimen282
\tikzsiblingdistance=\dimen283
\tikz@figbox=\box106
\tikz@figbox@bg=\box107
\tikz@tempbox=\box108
\tikz@tempbox@bg=\box109
\tikztreelevel=\count301
\tikznumberofchildren=\count302
\tikznumberofcurrentchild=\count303
\tikz@fig@count=\count304

(/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfmatrixcurrentrow=\count305
\pgfmatrixcurrentcolumn=\count306
\pgf@matrix@numberofcolumns=\count307
)
\tikz@expandcount=\count308

(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)))
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplots.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplotscore.code.tex
\t@pgfplots@toka=\toks35
\t@pgfplots@tokb=\toks36
\t@pgfplots@tokc=\toks37
\pgfplots@tmpa=\dimen284
\c@pgfplots@coordindex=\count309
\c@pgfplots@scanlineindex=\count310

(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/sys/pgfplotssysgeneric.code
.tex))
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/libs/pgfplotslibrary.code.t
ex)
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/oldpgfcompatib/pgfplotsoldp
gfsupp_loader.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibraryfpu.code.tex
))
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/util/pgfplotsutil.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotslists
tructure.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotslists
tructureext.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsarray
.code.tex
\c@pgfplotsarray@tmp=\count311
)
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsmatri
x.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/numtable/pgfplotstableshare
d.code.tex
\c@pgfplotstable@counta=\count312
\t@pgfplotstable@a=\toks38
)
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsdeque
.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/util/pgfplotsbinary.code.te
x
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/util/pgfplotsbinary.data.co
de.tex))
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/util/pgfplotsutil.verb.code
.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/libs/pgflibrarypgfplots.sur
fshading.code.tex
\c@pgfplotslibrarysurf@no=\count313

(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/sys/pgflibrarypgfplots.surf
shading.pgfsys-pdftex.def)))
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/util/pgfplotscolormap.code.
tex
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/util/pgfplotscolor.code.tex
))
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplotsstackedplots.code.t
ex)
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplotsplothandlers.code.t
ex
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplotsmeshplothandler.cod
e.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplotsmeshplotimage.code.
tex)))
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplots.scaling.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplotscoordprocessing.cod
e.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplots.errorbars.code.tex
) (/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplots.markers.code.tex
) (/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplotsticks.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplots.paths.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarydecorations.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmoduledecorations.cod
e.tex
\pgfdecoratedcompleteddistance=\dimen285
\pgfdecoratedremainingdistance=\dimen286
\pgfdecoratedinputsegmentcompleteddistance=\dimen287
\pgfdecoratedinputsegmentremainingdistance=\dimen288
\pgf@decorate@distancetomove=\dimen289
\pgf@decorate@repeatstate=\count314
\pgfdecorationsegmentamplitude=\dimen290
\pgfdecorationsegmentlength=\dimen291
)
\tikz@lib@dec@box=\box110
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarydecorations.pathmorphing.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrary
decorations.pathmorphing.code.tex))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarydecorations.pathreplacing.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrary
decorations.pathreplacing.code.tex))
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/libs/tikzlibrarypgfplots.co
ntourlua.code.tex)
\pgfplots@numplots=\count315
\pgfplots@xmin@reg=\dimen292
\pgfplots@xmax@reg=\dimen293
\pgfplots@ymin@reg=\dimen294
\pgfplots@ymax@reg=\dimen295
\pgfplots@zmin@reg=\dimen296
\pgfplots@zmax@reg=\dimen297
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibraryplotmarks.code.tex
File: tikzlibraryplotmarks.code.tex 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibraryplotmarks.co
de.tex
File: pgflibraryplotmarks.code.tex 2021/05/15 v3.1.9a (3.1.9a)
))) (/usr/share/texlive/texmf-dist/tex/latex/tcolorbox/tcolorbox.sty
Package: tcolorbox 2022/01/07 version 5.0.2 text color boxes

(/usr/share/texlive/texmf-dist/tex/latex/tools/verbatim.sty
Package: verbatim 2020-07-07 v1.5u LaTeX2e package for verbatim enhancements
\every@verbatim=\toks39
\verbatim@line=\toks40
\verbatim@in@stream=\read4
)
(/usr/share/texlive/texmf-dist/tex/latex/environ/environ.sty
Package: environ 2014/05/04 v0.3 A new way to define environments

(/usr/share/texlive/texmf-dist/tex/latex/trimspaces/trimspaces.sty
Package: trimspaces 2009/09/17 v1.1 Trim spaces around a token list
)
\@envbody=\toks41
)
(/usr/share/texlive/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count316
)
\tcb@titlebox=\box111
\tcb@upperbox=\box112
\tcb@lowerbox=\box113
\tcb@phantombox=\box114
\c@tcbbreakpart=\count317
\c@tcblayer=\count318
\c@tcolorbox@number=\count319
\tcb@temp=\box115
\tcb@temp=\box116
\tcb@temp=\box117
\tcb@temp=\box118
)
(/usr/share/texlive/texmf-dist/tex/latex/fontawesome5/fontawesome5.sty
(/usr/share/texlive/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2022-01-21 L3 programming layer (loader) 

(/usr/share/texlive/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2022-01-12 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count320
\l__pdf_internal_box=\box119
))
Package: fontawesome5 2021/06/04 v5.15.3 Font Awesome 5

(/usr/share/texlive/texmf-dist/tex/latex/l3packages/l3keys2e/l3keys2e.sty
Package: l3keys2e 2022-01-12 LaTeX2e option processing using LaTeX3 keys
)
(/usr/share/texlive/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2022-01-12 L3 Experimental document command parser
)
(/usr/share/texlive/texmf-dist/tex/latex/fontawesome5/fontawesome5-generic-help
er.sty
Package: fontawesome5-generic-helper 2021/06/04 v5.15.3 non-uTeX helper for fon
tawesome5

(/usr/share/texlive/texmf-dist/tex/latex/fontawesome5/fontawesome5-mapping.def)
)) (/usr/share/texlive/texmf-dist/tex/latex/colortbl/colortbl.sty
Package: colortbl 2020/01/04 v1.0e Color table columns (DPC)
\everycr=\toks42
\minrowclearance=\skip57
)
(/usr/share/texlive/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen298
\lightrulewidth=\dimen299
\cmidrulewidth=\dimen300
\belowrulesep=\dimen301
\belowbottomsep=\dimen302
\aboverulesep=\dimen303
\abovetopsep=\dimen304
\cmidrulesep=\dimen305
\cmidrulekern=\dimen306
\defaultaddspace=\dimen307
\@cmidla=\count321
\@cmidlb=\count322
\@aboverulesep=\dimen308
\@belowrulesep=\dimen309
\@thisruleclass=\count323
\@lastruleclass=\count324
\@thisrulewidth=\dimen310
)
(/usr/share/texlive/texmf-dist/tex/latex/tools/tabularx.sty
Package: tabularx 2020/01/15 v2.11c `tabularx' package (DPC)
\TX@col@width=\dimen311
\TX@old@table=\dimen312
\TX@old@col=\dimen313
\TX@target=\dimen314
\TX@delta=\dimen315
\TX@cols=\count325
\TX@ftn=\toks43
)
(/usr/share/texlive/texmf-dist/tex/latex/fancyhdr/fancyhdr.sty
Package: fancyhdr 2021/01/28 v4.0.1 Extensive control of page headers and foote
rs
\f@nch@headwidth=\skip58
\f@nch@O@elh=\skip59
\f@nch@O@erh=\skip60
\f@nch@O@olh=\skip61
\f@nch@O@orh=\skip62
\f@nch@O@elf=\skip63
\f@nch@O@erf=\skip64
\f@nch@O@olf=\skip65
\f@nch@O@orf=\skip66
)
(/usr/share/texlive/texmf-dist/tex/latex/wrapfig/wrapfig.sty
\wrapoverhang=\dimen316
\WF@size=\dimen317
\c@WF@wrappedlines=\count326
\WF@box=\box120
\WF@everypar=\toks44
Package: wrapfig 2003/01/31  v 3.6
)
(/usr/share/texlive/texmf-dist/tex/latex/eurosym/eurosym.sty
Package: eurosym 1998/08/06 v1.1 European currency symbol ``Euro''
\@eurobox=\box121
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibraryshapes.geometric.code.tex
File: tikzlibraryshapes.geometric.code.tex 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshape
s.geometric.code.tex
File: pgflibraryshapes.geometric.code.tex 2021/05/15 v3.1.9a (3.1.9a)
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibraryarrows.code.tex
File: tikzlibraryarrows.code.tex 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibraryarrows.code.
tex
File: pgflibraryarrows.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\arrowsize=\dimen318
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarypositioning.code.tex
File: tikzlibrarypositioning.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarycalc.code.tex
File: tikzlibrarycalc.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarypatterns.code.tex
File: tikzlibrarypatterns.code.tex 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibrarypatterns.cod
e.tex
File: pgflibrarypatterns.code.tex 2021/05/15 v3.1.9a (3.1.9a)
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibraryshadows.code.tex
File: tikzlibraryshadows.code.tex 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibraryfadings.code.tex
File: tikzlibraryfadings.code.tex 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibraryfadings.code
.tex
File: pgflibraryfadings.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)))
LaTeX Font Info:    Trying to load font information for T1+ptm on input line 61
.
 (/usr/share/texlive/texmf-dist/tex/latex/psnfss/t1ptm.fd
File: t1ptm.fd 2001/06/04 font definitions for T1/ptm.
)
(./journal.aux
(/usr/share/texlive/texmf-dist/tex/generic/babel/locale/en/babel-english.tex
Package babel Info: Importing basic data for english
(babel)             from babel-en.ini. Reported on input line 11.
))
\openout1 = `journal.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 61.
LaTeX Font Info:    ... okay on input line 61.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 61.
LaTeX Font Info:    ... okay on input line 61.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 61.
LaTeX Font Info:    ... okay on input line 61.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 61.
LaTeX Font Info:    ... okay on input line 61.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 61.
LaTeX Font Info:    ... okay on input line 61.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 61.
LaTeX Font Info:    ... okay on input line 61.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 61.
LaTeX Font Info:    ... okay on input line 61.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 61.
LaTeX Font Info:    ... okay on input line 61.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 61.
LaTeX Font Info:    ... okay on input line 61.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(34.14322pt, 529.22144pt, 34.14322pt)
* v-part:(T,H,B)=(42.67912pt, 768.2245pt, 34.14322pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=529.22144pt
* \textheight=768.2245pt
* \oddsidemargin=-38.12677pt
* \evensidemargin=-38.12677pt
* \topmargin=-66.59087pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=11.0pt
* \footskip=30.0pt
* \marginparwidth=50.0pt
* \marginparsep=10.0pt
* \columnsep=42.67912pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

\big@size=\dimen319
(/usr/share/texlive/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count327
\scratchdimen=\dimen320
\scratchbox=\box122
\nofMPsegments=\count328
\nofMParguments=\count329
\everyMPshowfont=\toks45
\MPscratchCnt=\count330
\MPscratchDim=\dimen321
\MPnumerator=\count331
\makeMPintoPDFobject=\count332
\everyMPtoPDFconversion=\toks46
) (/usr/share/texlive/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(/usr/share/texlive/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Liv
e
))
Package hyperref Info: Link coloring OFF on input line 61.

(/usr/share/texlive/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2021-04-02 v2.47 Cross-referencing by name of section

(/usr/share/texlive/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(/usr/share/texlive/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count333
)
LaTeX Info: Redefining \ref on input line 61.
LaTeX Info: Redefining \pageref on input line 61.
LaTeX Info: Redefining \nameref on input line 61.

(./journal.out) (./journal.out)
\@outlinefile=\write4
\openout4 = `journal.out'.

Package caption Info: Begin \AtBeginDocument code.
Package caption Info: wrapfig package is loaded.
Package caption Info: End \AtBeginDocument code.

Package pgfplots notification 'compat/show suggested version=true': you might b
enefit from \pgfplotsset{compat=1.18} (current compat level: 1.17).

<images/client_logo.jpg, id=31, 96.36pt x 48.18pt>
File: images/client_logo.jpg Graphic file (type jpg)
<use images/client_logo.jpg>
Package pdftex.def Info: images/client_logo.jpg  used on input line 70.
(pdftex.def)             Requested size: 75.11224pt x 37.55595pt.
LaTeX Font Info:    Trying to load font information for OT1+ztmcm on input line
 71.
(/usr/share/texlive/texmf-dist/tex/latex/psnfss/ot1ztmcm.fd
File: ot1ztmcm.fd 2000/01/03 Fontinst v1.801 font definitions for OT1/ztmcm.
)
LaTeX Font Info:    Trying to load font information for OML+ztmcm on input line
 71.

(/usr/share/texlive/texmf-dist/tex/latex/psnfss/omlztmcm.fd
File: omlztmcm.fd 2000/01/03 Fontinst v1.801 font definitions for OML/ztmcm.
)
LaTeX Font Info:    Trying to load font information for OMS+ztmcm on input line
 71.

(/usr/share/texlive/texmf-dist/tex/latex/psnfss/omsztmcm.fd
File: omsztmcm.fd 2000/01/03 Fontinst v1.801 font definitions for OMS/ztmcm.
)
LaTeX Font Info:    Trying to load font information for OMX+ztmcm on input line
 71.

(/usr/share/texlive/texmf-dist/tex/latex/psnfss/omxztmcm.fd
File: omxztmcm.fd 2000/01/03 Fontinst v1.801 font definitions for OMX/ztmcm.
)
LaTeX Font Info:    Trying to load font information for OT1+ptm on input line 7
1.

(/usr/share/texlive/texmf-dist/tex/latex/psnfss/ot1ptm.fd
File: ot1ptm.fd 2001/06/04 font definitions for OT1/ptm.
)
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <10.95> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 71.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <8> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 71.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <6> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 71.
<images/logo.png, id=32, 376.40625pt x 376.40625pt>
File: images/logo.png Graphic file (type png)
<use images/logo.png>
Package pdftex.def Info: images/logo.png  used on input line 87.
(pdftex.def)             Requested size: 60.09001pt x 60.08838pt.
LaTeX Font Info:    Trying to load font information for TS1+ptm on input line 9
7.

(/usr/share/texlive/texmf-dist/tex/latex/psnfss/ts1ptm.fd
File: ts1ptm.fd 2001/06/04 font definitions for TS1/ptm.
) [1

{/var/lib/texmf/fonts/map/pdftex/updmap/pdftex.map} <./images/client_logo.jpg> 
<./images/logo.png>]
Underfull \hbox (badness 1533) in paragraph at lines 189--190
\T1/ptm/m/n/10.95 traditional craftsmanship with modern operational efficiency.
 The company's strategic focus on
 []

[2]
Underfull \hbox (badness 10000) in paragraph at lines 206--207
[]\T1/ptm/b/n/10.95 Industry: \T1/ptm/m/n/10.95 Meat Processing & Food
 []


Underfull \hbox (badness 1565) in paragraph at lines 268--269
\T1/ptm/m/n/10.95 diversified product portfolio, strong international presence,
 established distribution networks, and
 []

[3] [4]
Missing character: There is no a in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no v in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no m in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no v in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no m in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no v in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no m in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no v in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no m in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no v in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no m in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no v in font nullfont!
Missing character: There is no o in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no m in font nullfont!
Missing character: There is no i in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no m in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no y in font nullfont!
Missing character: There is no m in font nullfont!
Missing character: There is no f in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no y in font nullfont!
[5]
Underfull \hbox (badness 6910) in paragraph at lines 486--487
[]\T1/ptm/b/n/10.95 North America: \T1/ptm/m/n/10.95 USA, Canada (premium
 []


Underfull \hbox (badness 10000) in paragraph at lines 524--525
[]\T1/ptm/b/n/10.95 Production Optimization: \T1/ptm/m/n/10.95 +12% yield
 []


Underfull \hbox (badness 10000) in paragraph at lines 526--527
[]\T1/ptm/b/n/10.95 Technology Integration: \T1/ptm/m/n/10.95 IoT monitoring
 []


Underfull \hbox (badness 10000) in paragraph at lines 608--609
[]\T1/ptm/b/n/10.95 Supplier Diversification: \T1/ptm/m/n/10.95 Multiple source

 []

[6]
Overfull \hbox (14.60616pt too wide) in paragraph at lines 627--645
 [] 
 []


Package caption Warning: Unused \captionsetup[figure] on input line 16.
See the caption package documentation for explanation.

[7] (./journal.aux)
Package rerunfilecheck Info: File `journal.out' has not changed.
(rerunfilecheck)             Checksum: D41D8CD98F00B204E9800998ECF8427E;0.
 ) 
Here is how much of TeX's memory you used:
 40263 strings out of 480209
 942846 string characters out of 5894582
 1323215 words of memory out of 5000000
 57590 multiletter control sequences out of 15000+600000
 510746 words of font info for 76 fonts, out of 8000000 for 9000
 14 hyphenation exceptions out of 8191
 99i,12n,103p,728b,2330s stack positions out of 5000i,500n,10000p,200000b,80000s
{/usr/share/texlive/texmf-dist/fonts/enc/dvips/base/8r.enc}</usr/share/texliv
e/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb></usr/share/texlive/texm
f-dist/fonts/type1/urw/times/utmb8a.pfb></usr/share/texlive/texmf-dist/fonts/ty
pe1/urw/times/utmr8a.pfb></usr/share/texlive/texmf-dist/fonts/type1/urw/times/u
tmri8a.pfb>
Output written on journal.pdf (7 pages, 176419 bytes).
PDF statistics:
 109 PDF objects out of 1000 (max. 8388607)
 72 compressed objects within 1 object stream
 22 named destinations out of 1000 (max. 500000)
 119 words of extra memory for PDF output out of 10000 (max. 10000000)

